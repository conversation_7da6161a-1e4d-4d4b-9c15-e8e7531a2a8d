"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_index = require("../../../utils/index.js");
const utils_http = require("../../../utils/http.js");
const store_user = require("../../../store/user.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_rate2 = common_vendor.resolveComponent("up-rate");
  (_easycom_up_icon2 + _easycom_up_loading_icon2 + _easycom_up_rate2)();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_loading_icon = () => "../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_rate = () => "../../../node-modules/uview-plus/components/u-rate/u-rate.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_loading_icon + _easycom_up_rate + toast)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "assessment",
  setup(__props) {
    var _a, _b;
    const ref_toast = common_vendor.ref();
    const store = store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    const userInfo = common_vendor.ref(store.routerUserinfo);
    const tiBig = common_vendor.ref(0);
    const tiSmall = common_vendor.ref(0);
    if (store.submitData.TId == 0) {
      userInfo.value = userStore.userInfo;
    } else {
      userInfo.value = store.routerUserinfo;
      store.submitData.BId = (_a = userInfo.value) == null ? void 0 : _a.ID;
    }
    if (((_b = userInfo.value.Avatar) == null ? void 0 : _b.length) === 0 || !userInfo.value.Avatar) {
      userInfo.value.Avatar = "/static/images/avatar.png";
    }
    const questionRenderIndex = common_vendor.ref(0);
    const orginalList = common_vendor.ref([]);
    const questionList = common_vendor.ref([]);
    const totalValueList = common_vendor.ref([]);
    const contentList = common_vendor.ref([]);
    const currentScore = common_vendor.ref(0);
    const full = async (isFull = false) => {
      return await new Promise((r) => {
        if (questionRenderIndex.value >= 8 && questionRenderIndex.value <= 10 && isFull === false) {
          questionList.value.map((item, _index) => {
            if (Number((Math.random() * 10).toFixed(0)) >= 5 && _index % 2 === 0) {
              item.value = 5;
            } else {
              item.value = 4;
            }
          });
          return r(true);
        }
        questionList.value.map((item) => {
          item.value = 5;
        });
        r(true);
      });
    };
    const back = () => {
      if (questionRenderIndex.value > 0) {
        common_vendor.index.showModal({
          title: "警告",
          content: "你有未完成表单是否继续退出？",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateBack({
                delta: 1
              });
            }
          }
        });
      } else {
        common_vendor.index.navigateBack({
          delta: 1
        });
      }
    };
    const changeUser = function() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    const computeValue = (list, fractions = []) => {
      const currentWeights = orginalList.value[questionRenderIndex.value].Weights;
      if (fractions.length === 0 || fractions.length !== list.length) {
        const sum = list.reduce((a, b) => a + b, 0);
        const data = sum / (list.length * 5) * currentWeights;
        return data.toFixed(2);
      }
      let weightedSum = 0;
      for (let i = 0; i < list.length; i++) {
        const maxScore = 100 * (currentWeights / 100) * (fractions[i] / 100);
        const starValue = maxScore / 5;
        const score = starValue * list[i];
        weightedSum += score;
      }
      return weightedSum.toFixed(2);
    };
    const backQuestion = async () => {
      questionRenderIndex.value -= 1;
      totalValueList.value.splice(questionRenderIndex.value, 1);
      questionList.value = orginalList.value[questionRenderIndex.value].List;
      const backCount = orginalList.value[questionRenderIndex.value].List.length;
      const len = contentList.value.length;
      questionList.value.map((item, index) => {
        console.log(contentList.value[len - backCount].Value);
        item.value = Number(contentList.value[len - (backCount - index)].Value);
      });
      contentList.value.splice(len - backCount, backCount);
      return;
    };
    const clearData = () => {
      store.resetData();
      questionRenderIndex.value = 0;
      totalValueList.value = [];
      contentList.value = [];
    };
    const nextPage = async function() {
      var _a2, _b2;
      if (questionRenderIndex.value > orginalList.value.length) {
        clearData();
        return;
      }
      if ((_a2 = questionList.value) == null ? void 0 : _a2.find((item) => item.value === 0)) {
        (_b2 = ref_toast.value) == null ? void 0 : _b2.warning("您有未完成的题目");
        return;
      }
      const list = questionList.value.map((item) => item.value);
      orginalList.value[questionRenderIndex.value].Weights;
      let hasFractions = questionList.value.every(
        (item) => item.Fraction !== void 0 && item.Fraction > 0
      );
      if (!hasFractions) {
        const averageFraction = 100 / questionList.value.length;
        questionList.value.forEach((item) => {
          item.Fraction = averageFraction;
        });
      } else {
        const totalFraction = questionList.value.reduce(
          (sum, item) => sum + (item.Fraction || 0),
          0
        );
        if (Math.abs(totalFraction - 100) > 1e-3) {
          const ratio = 100 / totalFraction;
          questionList.value.forEach((item) => {
            if (item.Fraction) {
              item.Fraction = Number((item.Fraction * ratio).toFixed(2));
            }
          });
        }
      }
      const fractions = questionList.value.map((item) => item.Fraction || 0);
      totalValueList.value[questionRenderIndex.value] = Number(
        computeValue(list, fractions)
      );
      questionList.value.forEach(
        (item) => contentList.value.push({ Id: item.Id, Value: String(item.value) })
      );
      questionRenderIndex.value += 1;
      currentScore.value = totalValueList.value.reduce((a, b) => a + b, 0);
      if (questionRenderIndex.value === orginalList.value.length) {
        const sum = totalValueList.value.reduce((a, b) => a + b, 0);
        store.submitData.Score = `${sum.toFixed(2)}`;
        store.submitData.Content = contentList.value;
        common_vendor.index.navigateTo({
          url: "./submit?tiBig=" + tiBig.value + "&tiSmall=" + tiSmall.value
        });
        return;
      }
      if (questionRenderIndex.value < orginalList.value.length) {
        return await getQuestionsAPI();
      }
    };
    const getQuestionsAPI = async function() {
      var _a2, _b2;
      const Id = store.routerUserinfo.DepartmentId || 0;
      const res = await utils_http.getQuestions({
        DepartmentId: Id,
        TargetUserId: store.submitData.BId
      });
      console.log("题目", res);
      if (!res || !res.data || !res.data.Data) {
        (_b2 = ref_toast.value) == null ? void 0 : _b2.info(((_a2 = res == null ? void 0 : res.data) == null ? void 0 : _a2.Message) || "获取题目失败");
        return;
      }
      const data = res.data.Data.listjson;
      tiBig.value = data.length;
      tiSmall.value = data.reduce((total, item) => {
        var _a3;
        return total + (((_a3 = item.List) == null ? void 0 : _a3.length) || 0);
      }, 0);
      data.sort((a, b) => a.Id - b.Id);
      data.map((item) => {
        if (item.List.length > 0) {
          const averageFraction = 100 / item.List.length;
          item.List.map((item2) => {
            item2.value = 0;
            if (!item2.Fraction) {
              item2.Fraction = averageFraction;
            }
          });
          const totalFraction = item.List.reduce(
            (sum, item2) => sum + (item2.Fraction || 0),
            0
          );
          if (Math.abs(totalFraction - 100) > 1e-3) {
            const ratio = 100 / totalFraction;
            item.List.forEach((item2) => {
              if (item2.Fraction) {
                item2.Fraction = Number((item2.Fraction * ratio).toFixed(2));
              }
            });
          }
        }
      });
      orginalList.value = data;
      if (!data[questionRenderIndex.value]) {
        setTimeout(() => {
          var _a3;
          return (_a3 = ref_toast.value) == null ? void 0 : _a3.fail("题库丢失");
        }, 2e3);
        return;
      }
      questionList.value = data[questionRenderIndex.value].List;
    };
    getQuestionsAPI();
    common_vendor.onMounted(() => {
      if (document) {
        document.addEventListener("keydown", function(event) {
          if (event.key === "c" || event.key === "C") {
            full();
          }
        });
      }
      common_vendor.onShow(() => {
        if (common_vendor.index.getStorageSync("jianyi") || false) {
          questionRenderIndex.value -= 1;
          common_vendor.index.removeStorageSync("jianyi");
          return;
        }
      });
    });
    common_vendor.watch(totalValueList.value, (n) => {
    });
    common_vendor.watch(questionList.value, (n) => {
      console.log("questionList.value", n);
    });
    return (_ctx, _cache) => {
      var _a2, _b2;
      return common_vendor.e({
        a: common_vendor.o(($event) => back()),
        b: common_vendor.t(userInfo.value.DisplayName != common_vendor.unref(userStore).userInfo.DisplayName ? "被评者" : "姓名"),
        c: common_vendor.t(userInfo.value.DisplayName),
        d: common_vendor.unref(store).submitData.TId != 0
      }, common_vendor.unref(store).submitData.TId != 0 ? {
        e: common_vendor.p({
          name: "person-delete-fill",
          size: "3vh",
          color: "crimson"
        }),
        f: common_vendor.o(($event) => changeUser())
      } : {}, {
        g: common_vendor.t(userInfo.value.Ex4 || "无"),
        h: common_vendor.t(common_vendor.unref(utils_index.getCurrentDate)()),
        i: common_vendor.unref(utils_index.cantBeBad)(userInfo.value.Avatar) || "../../../static/images/avatar.png",
        j: common_vendor.t((_a2 = orginalList.value[questionRenderIndex.value]) == null ? void 0 : _a2.Name),
        k: questionList.value.length === 0
      }, questionList.value.length === 0 ? {
        l: common_vendor.p({
          size: "3vh"
        })
      } : {}, {
        m: common_vendor.f(questionList.value, (item1, index2, i0) => {
          return {
            a: common_vendor.t(index2 + 1),
            b: common_vendor.t(item1.Content),
            c: "448cd879-2-" + i0,
            d: common_vendor.o(($event) => item1.value = $event, item1.Id),
            e: common_vendor.p({
              count: 5,
              allowHalf: true,
              size: "3.2vh",
              inactiveColor: "#a6bcc8",
              modelValue: item1.value
            }),
            f: common_vendor.t(item1.value),
            g: item1.Id
          };
        }),
        n: common_vendor.t(questionRenderIndex.value + 1),
        o: common_vendor.t((_b2 = orginalList.value) == null ? void 0 : _b2.length),
        p: common_vendor.o(($event) => backQuestion()),
        q: questionRenderIndex.value > 0,
        r: common_vendor.o(($event) => nextPage()),
        s: common_vendor.sr(ref_toast, "448cd879-3", {
          "k": "ref_toast"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-448cd879"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/index/views/assessment.vue"]]);
wx.createPage(MiniProgramPage);
