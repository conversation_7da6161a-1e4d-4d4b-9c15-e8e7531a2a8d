"use strict";
const common_vendor = require("../common/vendor.js");
const utils_index = require("./index.js");
const store_user = require("../store/user.js");
const store_pinia = require("../store/pinia.js");
const utils_http = require("./http.js");
const utils_sign = require("./sign.js");
let isCheckingLogin = false;
const reset401Status = () => {
  isCheckingLogin = false;
};
const getBaseUrl = () => {
  return "https://360.hlktech.com/Api/V1";
};
let baseUrl = getBaseUrl();
const userStore = store_user.useUserStore(store_pinia.pinia);
const httpInterceptor = {
  // 拦截前触发
  invoke(options) {
    options.timeout = 5e3;
    const headOptions = {
      "Content-Type": "application/x-www-form-urlencoded",
      //添加 token
      authorization: "Bearer " + common_vendor.index.getStorageSync("AccessToken"),
      ...options.header
    };
    options.header = utils_sign.sign(headOptions, "HlkTech20200429");
    console.log("全部", options);
  }
};
common_vendor.index.addInterceptor("request", httpInterceptor);
common_vendor.index.addInterceptor("uploadFile", httpInterceptor);
const request = async (config) => {
  if (config.url != "/Http2/GetEvaluation" && config.url != "/Http2/GetTimeline" && config.url != "/Http2/GetDepartList" && config.url != "/Http2/GetUserList" && config.url != "/Http2/GetUserDetail")
    ;
  return await common_vendor.index.request({
    method: config.method ?? "POST",
    header: config.header,
    //url:  `${baseUrl}`,
    url: `${baseUrl}` + config.url,
    data: config.data
  }).then(async (res) => {
    console.log("这里", res);
    return await dealWidthTheCodeFail(res);
  }).catch((err) => {
    common_vendor.index.hideLoading();
    console.log("请求报错 -->", err);
    return new Error("请求报错");
  });
};
const dealWidthTheCodeFail = async (res) => {
  return await new Promise((r, j) => {
    var _a;
    if (res.statusCode !== 200) {
      common_vendor.index.hideLoading();
      let networkError = "";
      switch (res.statusCode) {
        case 400:
          networkError = "服务器出错";
          break;
        case 401:
          console.log("过期");
          networkError = "您的身份信息已失效";
          break;
        case 403:
          networkError = "您的访问已被禁止";
          break;
        case 404:
          networkError = "访问内容已丢失";
          break;
        case 500:
          networkError = "网络请求失败";
          break;
        default:
          networkError = "遇到未知网络错误";
          break;
      }
      utils_index.uniToast(networkError);
      j(new Error(networkError));
      return;
    }
    const code = (_a = res.data) == null ? void 0 : _a.Code;
    let errText = "";
    switch (code) {
      case 1:
        common_vendor.index.hideLoading();
        r(res);
        return;
      case 2:
        common_vendor.index.hideLoading();
        r(res);
        return;
      case 3:
        common_vendor.index.hideLoading();
        console.log(333, res);
        isCheckingLogin = false;
        common_vendor.index.showToast({ icon: "none", title: "请重新登录" });
        setTimeout(() => {
          common_vendor.index.redirectTo({
            url: "/pages/login/login"
          });
        }, 1e3);
        j(new Error("请重新登录"));
        return;
      case 400:
        common_vendor.index.hideLoading();
        errText = "服务器出错";
        break;
      case 401:
        common_vendor.index.hideLoading();
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "系统提示",
            content: "您的身份信息已失效🐿️",
            confirmText: "重新授权",
            showCancel: false,
            confirmColor: "#1e90ff",
            success: () => {
              checkLogin().finally(() => {
                setTimeout(() => {
                }, 2e3);
              });
            }
          });
        }, 500);
        j(new Error("身份验证已过期"));
        return;
      case 403:
        common_vendor.index.hideLoading();
        errText = "您的访问已被禁止";
        break;
      case 404:
        common_vendor.index.hideLoading();
        errText = "访问内容已丢失";
        break;
      case 500:
        common_vendor.index.hideLoading();
        errText = "网络请求失败";
        break;
      default:
        common_vendor.index.hideLoading();
        errText = "遇到未知业务错误";
        break;
    }
    setTimeout(() => {
      utils_index.uniToast(errText);
      j(new Error(errText));
    }, 2e3);
  });
};
const checkLogin = async () => {
  if (isCheckingLogin) {
    console.log("防止重复执行checkLogin");
    return;
  }
  isCheckingLogin = true;
  try {
    if (!common_vendor.index.getStorageSync("AccessToken") || !common_vendor.index.getStorageSync("openid")) {
      console.log(444);
      common_vendor.index.showToast({ icon: "none", title: "无效授权，请验证密码" });
      setTimeout(() => {
        common_vendor.index.redirectTo({
          url: "/pages/login/login"
        });
      }, 200);
      return;
    }
    await utils_index.getCurrentEnvironment().then(async (url) => {
      if (url.length >= 5) {
        const res = await utils_http.refreshToken({
          OpenId: common_vendor.index.getStorageSync("openid")
        });
        if (!res.data.Data) {
          common_vendor.index.showToast({ icon: "none", title: res.data.Message });
          return;
        }
        common_vendor.index.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
        common_vendor.index.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
        userStore.userInfo = res.data.Data.User;
        console.log("刷新token -->", res);
      }
    }).catch((err) => {
      console.error("环境检测失败:", err);
    });
  } catch (error) {
    console.error("checkLogin error:", error);
  } finally {
    isCheckingLogin = false;
  }
};
exports.getBaseUrl = getBaseUrl;
exports.request = request;
exports.reset401Status = reset401Status;
