"use strict";
const common_vendor = require("../common/vendor.js");
const utils_index = require("./index.js");
const store_user = require("../store/user.js");
const store_pinia = require("../store/pinia.js");
const utils_http = require("./http.js");
const utils_sign = require("./sign.js");
let isHandling401 = false;
let isCheckingLogin = false;
let isRedirectingToLogin = false;
const reset401Status = () => {
  isHandling401 = false;
  isCheckingLogin = false;
  isRedirectingToLogin = false;
};
const getBaseUrl = () => {
  return "https://360.hlktech.com/Api/V1";
};
let baseUrl = getBaseUrl();
const userStore = store_user.useUserStore(store_pinia.pinia);
const httpInterceptor = {
  // 拦截前触发
  invoke(options) {
    options.timeout = 5e3;
    const headOptions = {
      "Content-Type": "application/x-www-form-urlencoded",
      //添加 token
      authorization: "Bearer " + common_vendor.index.getStorageSync("AccessToken"),
      ...options.header
    };
    options.header = utils_sign.sign(headOptions, "HlkTech20200429");
    console.log("全部", options);
  }
};
common_vendor.index.addInterceptor("request", httpInterceptor);
common_vendor.index.addInterceptor("uploadFile", httpInterceptor);
const request = async (config) => {
  if (config.url != "/Http2/GetEvaluation" && config.url != "/Http2/GetTimeline" && config.url != "/Http2/GetDepartList" && config.url != "/Http2/GetUserList" && config.url != "/Http2/GetUserDetail")
    ;
  return await common_vendor.index.request({
    method: config.method ?? "POST",
    header: config.header,
    //url:  `${baseUrl}`,
    url: `${baseUrl}` + config.url,
    data: config.data
  }).then(async (res) => {
    console.log("这里", res);
    if (res.statusCode === 200)
      return await dealWidthTheCodeFail(res);
    switch (res.statusCode) {
      case 400:
        utils_index.uniToast("服务器出错");
        break;
      case 401:
        console.log("过期");
        if (!isHandling401 && !isRedirectingToLogin) {
          isHandling401 = true;
          setTimeout(() => {
            common_vendor.index.showModal({
              title: "系统提示",
              content: "您的身份信息已失效🐿️",
              confirmText: "重新授权",
              showCancel: false,
              confirmColor: "#1e90ff",
              success: () => {
                checkLogin().finally(() => {
                  setTimeout(() => {
                    isHandling401 = false;
                  }, 2e3);
                });
              }
            });
          }, 500);
        }
        break;
      case 403:
        utils_index.uniToast("您的访问已被禁止");
        break;
      case 404:
        utils_index.uniToast("访问内容已丢失");
        break;
      case 500:
        utils_index.uniToast("网络请求失败");
        break;
      default:
        utils_index.uniToast("遇到未知错误");
        break;
    }
    common_vendor.index.hideLoading();
    return false;
  }).catch((err) => {
    common_vendor.index.hideLoading();
    console.log("请求报错 -->", err);
    return new Error("请求报错");
  });
};
const dealWidthTheCodeFail = async (res) => {
  const code = res.data.Code;
  return await new Promise((r, j) => {
    let errText = "";
    switch (code) {
      case 1:
        common_vendor.index.hideLoading();
        r(res);
        return;
      case 2:
        common_vendor.index.hideLoading();
        r(res);
        return;
      case 3:
        common_vendor.index.hideLoading();
        console.log(333, res);
        if (isRedirectingToLogin) {
          j(new Error("请重新登录"));
          return;
        }
        isRedirectingToLogin = true;
        isHandling401 = true;
        isCheckingLogin = false;
        common_vendor.index.showToast({ icon: "none", title: "请重新登录" });
        setTimeout(() => {
          common_vendor.index.redirectTo({
            url: "/pages/login/login"
          });
        }, 1e3);
        j(new Error("请重新登录"));
        return;
      case 400:
        common_vendor.index.hideLoading();
        errText = "服务器出错";
        break;
      case 401:
        common_vendor.index.hideLoading();
        if (isHandling401 || isRedirectingToLogin) {
          j(new Error("身份验证已过期"));
          return;
        }
        isHandling401 = true;
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "系统提示",
            content: "您的身份信息已失效🐿️",
            confirmText: "重新授权",
            showCancel: false,
            confirmColor: "#1e90ff",
            success: () => {
              checkLogin().finally(() => {
                setTimeout(() => {
                  isHandling401 = false;
                }, 2e3);
              });
            }
          });
        }, 500);
        j(new Error("身份验证已过期"));
        return;
      case 403:
        common_vendor.index.hideLoading();
        errText = "您的访问已被禁止";
        break;
      case 404:
        common_vendor.index.hideLoading();
        errText = "访问内容已丢失";
        break;
      case 500:
        common_vendor.index.hideLoading();
        errText = "网络请求失败";
        break;
      default:
        common_vendor.index.hideLoading();
        errText = "遇到未知错误";
        break;
    }
    setTimeout(() => {
      common_vendor.index.hideLoading();
      utils_index.uniToast(errText);
      j(new Error(errText));
    }, 2e3);
  });
};
const checkLogin = async () => {
  if (isCheckingLogin) {
    console.log("防止重复执行checkLogin");
    return;
  }
  isCheckingLogin = true;
  try {
    if (!common_vendor.index.getStorageSync("AccessToken") || !common_vendor.index.getStorageSync("openid")) {
      console.log(444);
      common_vendor.index.showToast({ icon: "none", title: "无效授权，请验证密码" });
      setTimeout(() => {
        common_vendor.index.redirectTo({
          url: "/pages/login/login"
        });
      }, 200);
      return;
    }
    await utils_index.getCurrentEnvironment().then(async (url) => {
      if (url.length >= 5) {
        const res = await utils_http.refreshToken({
          OpenId: common_vendor.index.getStorageSync("openid")
        });
        if (!res.data.Data) {
          common_vendor.index.showToast({ icon: "none", title: res.data.Message });
          return;
        }
        common_vendor.index.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
        common_vendor.index.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
        userStore.userInfo = res.data.Data.User;
        console.log("刷新token -->", res);
      }
    }).catch((err) => {
      console.error("环境检测失败:", err);
    });
  } catch (error) {
    console.error("checkLogin error:", error);
  } finally {
    isCheckingLogin = false;
  }
};
exports.getBaseUrl = getBaseUrl;
exports.request = request;
exports.reset401Status = reset401Status;
