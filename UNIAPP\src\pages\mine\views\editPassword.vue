<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <view class="title">修改密码</view>
    </view>
    <view class="main">
      <up-form
        labelPosition="left"
        :model="list"
        :rules="list.rules"
        ref="ref_form"
        :labelStyle="style">
        <view class="item" style="border-bottom: 1px solid #e5e5e5">
          <up-form-item :prop="`form.oldPassword`">
            <up-input
              class="input"
              type="text"
              v-model="list.form.oldPassword"
              border="none"
              placeholder="请输入旧密码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">旧密码</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <view class="item" style="border-bottom: 1px solid #e5e5e5">
          <up-form-item :prop="`form.newPassword`">
            <up-input
              class="input"
              type="password"
              v-model="list.form.newPassword"
              border="none"
              placeholder="请输入新密码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">新密码</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <view class="item">
          <up-form-item :prop="`form.confirmPassowrd`">
            <up-input
              class="input"
              type="password"
              v-model="list.form.confirmPassowrd"
              border="none"
              placeholder="请再次确认密码">
              <template #prefix>
                <view
                  style="
                    border-right: 1rpx solid #e5e5e5;
                    padding-right: 30rpx;
                    display: flex;
                    place-items: center;
                  ">
                  <view class="">确认密码</view>
                </view>
              </template>
            </up-input>
          </up-form-item>
        </view>
      </up-form>
      <view class="" style="margin-top: 50rpx; transition: all 0.5s">
        <button
          style="
            width: 80%;
            background: linear-gradient(80deg, #677bf0, #2330df);
            box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
            color: white;
            border-radius: 0rpx 41rpx 41rpx;
          "
          @click="Submit()">
          提交
        </button>
      </view>
      <!--  -->
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useIndexStore } from "@/store";
import toast from "@/components/toast.vue";
import type { IToast } from "@/types";
import { updatePassword } from "@/utils/http";
import { useUserStore } from "@/store/user";
import { generateHash } from "@/utils";

const style = ref({
  marginLeft: "0px",
  fontSize: "60px",
});
const store = useIndexStore();
const userStore = useUserStore();
const ref_toast = ref<IToast>();
const ref_form = ref<any>(null)!;
const list = ref({
  form: {
    oldPassword: "",
    newPassword: "",
    confirmPassowrd: "",
  },
  // { name: '旧密码', oldPassword: '', text: '请输入旧密码',key:'oldPassword',value:'' },
  // { name: '新密码', newPassword: '', text: '请输入新密码',key:'newPassword',value:'' },
  // { name: '确认密码', confirmPassowrd: '', text: '请在此确认密码',key:'confirmPassowrd',value:'' },
  rules: {
    "form.oldPassword": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        console.log("验证通过 -->", value);
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value?.length < 6) {
          callback(new Error("密码至少为6位"));
          return;
        }
        if (value?.length >= 28) {
          callback(new Error("密码输入过长"));
          return;
        }
        if (value === list.value.form.newPassword) {
          callback(new Error("新密码不得和旧密码相同"));
          return;
        }
        callback();
      },
    },
    "form.newPassword": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value?.length < 6) {
          callback(new Error("密码至少为6位"));
          return;
        }
        if (value?.length >= 28) {
          callback(new Error("密码输入过长"));
          return;
        }
        if (value === list.value.form.oldPassword) {
          callback(new Error("新密码不得和旧密码相同"));
          return;
        }
        callback();
        // console.log('验证通过 -->', value);
      },
    },
    "form.confirmPassowrd": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value: string, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value != list.value.form.newPassword) {
          callback(new Error("您两次输入的密码不相同"));
          return;
        }
        if (value === list.value.form.oldPassword) {
          callback(new Error("新密码不得和旧密码相同"));
          return;
        }
        callback();
        // console.log('验证通过 -->', value);
      },
    },
  },
});

const Submit = () => {
  ref_form.value
    ?.validate()
    .then(async (valid) => {
      if (!valid) return;
      // 成功
      await updatePasswordAPI();
    })
    .catch((err: any) => {
      // 处理验证错误
      console.log("校验失败", err);
      ref_toast.value?.warning("表单验证失败！");
    });
};

/** 修改密码API */
const updatePasswordAPI = async () => {
  const res = await updatePassword({
    pwd: generateHash(list.value.form.oldPassword),
    pwd2: generateHash(list.value.form.newPassword),
  });
  const data = res.data;
  if (data?.Code != 1) {
    ref_toast.value?.fail(data.Message);
    return;
  }
  console.log("修改密码结果-->", data);
  uni.showModal({
    title: "修改成功🦉",
    content: "请 您 重 新 登 录 🦅~",
    showCancel: false,
    confirmText: "好的🪶",
    success: () => {
      uni.showLoading({ title: "跳转中..." });
      userStore.resetLoginData();
      setTimeout(() => {
        uni.hideLoading();
        uni.redirectTo({
          url: "/pages/login/login", 
        });
      }, 1200);
    },
  });
  // ref_toast.value?.success('修改成功')
};
</script>

<style scoped>
.head {
  height: 170rpx;
  background-color: white;
  display: flex;
  place-items: center;
}

.head > view {
  margin-top: 40rpx;
}

.main {
  margin-top: 40rpx;
  width: 100%;
  height: 800rpx;
  /* border: 1px solid ; */
}

.item {
  width: 90%;
  height: 100rpx;
  padding: 10rpx 0rpx;
  margin: 35rpx auto;
  /* border: 1px solid gray; */
  border-bottom: 1px solid #e5e5e5;
  background-color: white;
  display: flex;
  color: #666666;
  place-items: center;
  position: relative;
}
/deep/ .u-input__content {
  font-size: 34rpx !important;
}
.item > view {
  width: 100%;
  padding: 20px;
  text-align: right;
}

.item > view > image {
  margin-top: 10rpx;
  width: 38rpx;
  height: 30rpx;
  /* border: 1px solid gray; */
}

.text {
  text-align: right;
  font-size: 20rpx;
  margin-right: 60rpx;
  padding-bottom: 10rpx;
  color: #888888;
  letter-spacing: 2rpx;
}

.input {
  letter-spacing: 3rpx;
}
</style>
<style>
/deep/ .u-form-item__body__right__message {
  position: absolute;
  top: 90rpx;
  text-align: left;
  width: auto !important;
  text-indent: 0px !important;
  /* border: 1px solid ; */
  margin-left: 120rpx !important;
  font-size: 24rpx !important;
  padding: 0px 30rpx;
  background-color: white;
}
</style>
