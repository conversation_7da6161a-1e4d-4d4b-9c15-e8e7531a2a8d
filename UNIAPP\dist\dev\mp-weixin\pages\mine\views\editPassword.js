"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_http = require("../../../utils/http.js");
const store_user = require("../../../store/user.js");
const utils_index = require("../../../utils/index.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  (_easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_form2)();
}
const _easycom_up_input = () => "../../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../../node-modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_form = () => "../../../node-modules/uview-plus/components/u-form/u-form.js";
if (!Math) {
  (_easycom_up_input + _easycom_up_form_item + _easycom_up_form + toast)();
}
const toast = () => "../../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "editPassword",
  setup(__props) {
    const style = common_vendor.ref({
      marginLeft: "0px",
      fontSize: "60px"
    });
    const store = store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    const ref_toast = common_vendor.ref();
    const ref_form = common_vendor.ref(null);
    const list = common_vendor.ref({
      form: {
        oldPassword: "",
        newPassword: "",
        confirmPassowrd: ""
      },
      // { name: '旧密码', oldPassword: '', text: '请输入旧密码',key:'oldPassword',value:'' },
      // { name: '新密码', newPassword: '', text: '请输入新密码',key:'newPassword',value:'' },
      // { name: '确认密码', confirmPassowrd: '', text: '请在此确认密码',key:'confirmPassowrd',value:'' },
      rules: {
        "form.oldPassword": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            console.log("验证通过 -->", value);
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if ((value == null ? void 0 : value.length) < 6) {
              callback(new Error("密码至少为6位"));
              return;
            }
            if ((value == null ? void 0 : value.length) >= 28) {
              callback(new Error("密码输入过长"));
              return;
            }
            if (value === list.value.form.newPassword) {
              callback(new Error("新密码不得和旧密码相同"));
              return;
            }
            callback();
          }
        },
        "form.newPassword": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if ((value == null ? void 0 : value.length) < 6) {
              callback(new Error("密码至少为6位"));
              return;
            }
            if ((value == null ? void 0 : value.length) >= 28) {
              callback(new Error("密码输入过长"));
              return;
            }
            if (value === list.value.form.oldPassword) {
              callback(new Error("新密码不得和旧密码相同"));
              return;
            }
            callback();
          }
        },
        "form.confirmPassowrd": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if (value != list.value.form.newPassword) {
              callback(new Error("您两次输入的密码不相同"));
              return;
            }
            if (value === list.value.form.oldPassword) {
              callback(new Error("新密码不得和旧密码相同"));
              return;
            }
            callback();
          }
        }
      }
    });
    const Submit = () => {
      var _a;
      (_a = ref_form.value) == null ? void 0 : _a.validate().then(async (valid) => {
        if (!valid)
          return;
        await updatePasswordAPI();
      }).catch((err) => {
        var _a2;
        console.log("校验失败", err);
        (_a2 = ref_toast.value) == null ? void 0 : _a2.warning("表单验证失败！");
      });
    };
    const updatePasswordAPI = async () => {
      var _a;
      const res = await utils_http.updatePassword({
        pwd: utils_index.generateHash(list.value.form.oldPassword),
        pwd2: utils_index.generateHash(list.value.form.newPassword)
      });
      const data = res.data;
      if ((data == null ? void 0 : data.Code) != 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.fail(data.Message);
        return;
      }
      console.log("修改密码结果-->", data);
      common_vendor.index.showModal({
        title: "修改成功🦉",
        content: "请 您 重 新 登 录 🦅~",
        showCancel: false,
        confirmText: "好的🪶",
        success: () => {
          common_vendor.index.showLoading({ title: "跳转中..." });
          userStore.resetLoginData();
          setTimeout(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.redirectTo({
              url: "/pages/login/login"
            });
          }, 1200);
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => common_vendor.unref(store).backAPage()),
        b: common_vendor.o(($event) => list.value.form.oldPassword = $event),
        c: common_vendor.p({
          type: "text",
          border: "none",
          placeholder: "请输入旧密码",
          modelValue: list.value.form.oldPassword
        }),
        d: common_vendor.p({
          prop: `form.oldPassword`
        }),
        e: common_vendor.o(($event) => list.value.form.newPassword = $event),
        f: common_vendor.p({
          type: "password",
          border: "none",
          placeholder: "请输入新密码",
          modelValue: list.value.form.newPassword
        }),
        g: common_vendor.p({
          prop: `form.newPassword`
        }),
        h: common_vendor.o(($event) => list.value.form.confirmPassowrd = $event),
        i: common_vendor.p({
          type: "password",
          border: "none",
          placeholder: "请再次确认密码",
          modelValue: list.value.form.confirmPassowrd
        }),
        j: common_vendor.p({
          prop: `form.confirmPassowrd`
        }),
        k: common_vendor.sr(ref_form, "7eb91fa3-0", {
          "k": "ref_form"
        }),
        l: common_vendor.p({
          labelPosition: "left",
          model: list.value,
          rules: list.value.rules,
          labelStyle: style.value
        }),
        m: common_vendor.o(($event) => Submit()),
        n: common_vendor.sr(ref_toast, "7eb91fa3-7", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7eb91fa3"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/mine/views/editPassword.vue"]]);
wx.createPage(MiniProgramPage);
