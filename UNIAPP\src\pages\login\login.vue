<template>
  <view class="login">
    <view class="header">
      <image
        class="login_head_bg"
        src="../../static/mine/login_head.png"
        mode=""></image>
    </view>
    <view class="main">
      <up-form
        labelPosition="left"
        :model="model"
        :rules="model.rules"
        ref="ref_form"
        :labelStyle="style">
        <up-form-item prop="form.mobile">
          <view class="cu-form-group inputBox" data-label="账号">
            <view class="title">
              <button
                class="cu-btn round sm"
                style="
                  font-size: var(--size-2);
                  letter-spacing: 2rpx;
                  color: var(--bk-3);
                  background-color: transparent;
                ">
                {{ list[0].name }}
              </button>
            </view>
            <up-input
              placeholder="请输入手机号"
              border="none"
              v-model="model.form.mobile"
              name="username"
              placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
          </view>
        </up-form-item>

        <!-- <up-form-item prop="form.verifyCode" v-if="isShow">
					<view class="cu-form-group inputBox" data-label="验证码">
						<input placeholder="请输入手机验证码" name="verifyCode"  v-model="model.form.verifyCode"
							placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
						<text class='text-blue verifyCode' style="color: var(--blue-2);font-size: var(--size-2);"
							@click="getCode()">{{codeText}}</text>
					</view>
				</up-form-item> -->

        <up-form-item prop="form.password">
          <view class="cu-form-group inputBox" data-label="密码">
            <up-input
              type="password"
              border="none"
              v-model="model.form.password"
              placeholder="请输入密码"
              name="password"
              placeholder-style="font-size:26rpx;letter-spacing: 6rpx;" />
          </view>
        </up-form-item>
      </up-form>

      <!-- <view class="fnBox1">
				<view style="color: #5266E2;" @click="toOptions()">一键获取手机号</view>
			</view> -->

      <!-- <view class="fnBox1">
				<view style="color: #5266E2;" @click="useWX()">使用微信授权登录</view>
			</view> -->

      <view class="cu-form-group" style="border: none">
        <button class="submit" @click="submit()">登录</button>
      </view>

      <view class="fnBox1" style="text-align: center; text-indent: 90rpx">
        忘记密码？
        <text style="color: #5266e2" @click="toOptions()">点我申诉</text>
      </view>
    </view>
    <toast ref="ref_toast"></toast>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useUserStore } from "../../store/user";
import toast from "../../components/toast.vue";
import { generateHash, phoneRegex } from "@/utils";
import { getOpenidAPI, login } from "@/utils/http";
import { reset401Status } from "@/utils/request";
import type { IToast } from "@/types";
import { useIndexStore } from "@/store";
// ------------------> -数据- <---------------------------
const ref_toast = ref<IToast>();
const ref_form = ref<any>(null)!;

// 页面挂载时重置401状态
onMounted(() => {
  reset401Status();
});
const style = ref({
  marginLeft: "0px",
  fontSize: "60px",
});
const store = useIndexStore();
const userStore = useUserStore();
// 使用 ref 创建响应式数据
const list = ref([
  {
    name: "+86 (中国)",
    color: "#ffaa7f",
    fontSize: "20",
  },
]);
// const isShow = ref(true); // 使用密码登录
const model = ref({
  form: {
    // mobile: "",
    // mobile: "18888888881",
    // mobile: "18538743360",
    // mobile: "18936261001",
    // password: "123456",
    password: "",
    // verifyCode: '',
  },
  rules: {
    "form.mobile": {
      type: "number",
      required: true,
      trigger: ["change"],
      asyncValidator: (rule, value, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请先输入手机号"));
          return;
        }
        if (value?.length > 11) {
          callback(new Error("手机号过长"));
          return;
        }
        if (!phoneRegex.test(value)) {
          callback(new Error("请输入正确的手机号"));
          return;
        }
        callback();
        // console.log('验证通过 -->', value);
      },
    },
    // 'form.verifyCode': {
    // 	type: 'number',
    // 	required: true,
    // 	message: '请输入手机验证码',
    // 	trigger: ['change'],
    // },
    "form.password": {
      type: "string",
      required: true,
      trigger: ["change", "blur"],
      asyncValidator: (rule, value, callback) => {
        if (String(value).length === 0) {
          callback(new Error("请输入密码"));
          return;
        }
        if (value?.length < 6) {
          callback(new Error("密码至少为6位"));
          return;
        }
        if (value?.length >= 28) {
          console.error({ value });
          callback(new Error("密码输入过长"));
          return;
        }
        callback();
        // console.log('验证通过 -->', value);
      },
    },
  },
});

// ------------------> - 函数 - <---------------------------
// 15361580137
/** 提交表单 */
const submit = () => {
  ref_form.value
    ?.validate()
    .then(async (valid) => {
      if (!valid) return;
      // 成功
      getOpenid();
    })
    .catch((err: any) => {
      // 处理验证错误
      console.log("校验失败", err);
      ref_toast.value?.warning("表单验证失败！");
      // ref_form.value?.clearValidate()
    });
};
const getOpenid = () => {
  uni.showLoading({ title: "获取中..." });
  uni.login({
    success: (res) => {
      console.log("code 信息 -->", res);
      getOpenidAPI({ code: res.code })
        .then(async (res) => {
          console.log("login--openid -->", res);
          if (res.statusCode === 200 || res.statusCode === 1) {
            uni.setStorageSync("openid", res.data.Data.openid);
            loginFn(res.data.Data.openid);
            return;
          } else {
            ref_toast.value?.info("openid缺失");
          }
        })
        .catch((err) => {
          uni.hideLoading();
          ref_toast.value?.netFail("网络请求失败");
          console.log("获取openid失败-->", err);
        });
    },
    fail: (err) => {
      console.log("调用失败，您当前不在微信环境 -->", err);
      ref_toast.value?.netFail("请勿运行于非微信环境！");
    },
  });
};
const loginFn = async (openid?: string) => {
  if (openid) {
    //浏览器调试登录用
    model.value.form.openid = openid;
  }
  // model.value.form.password = generateHash(model.value.form.password);
  const res = await login(model.value.form);
  console.log("登录结果 -->", res);
  if (!res) return;
  if (res.data.Code === 1) {
    ref_toast.value?.success(res.data.Message ?? "登录成功!");
    uni.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
    uni.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
    // uni.setStorageSync('userInfo', JSON.stringify(res.data.Data.User))
    userStore.userInfo = res.data.Data.User;
    // userStore.userInfo.DisplayName =  res.data.Data.User.DisplayName
    // userStore.userInfo.Avatar =  res.data.Data.User.Avatar
    // userStore.userInfo.department = res.data?.ExtData?.Ex4 || '未知' //如果没绑定openid 就不会有数据
    // userStore.userInfo.Mobile =  res.data.Data.User.Mobile
    // userStore.userInfo.Mail =  res.data.Data.User.Mail
    // userStore.userInfo.Sex =  res.data.Data.User.Sex

    // 登录成功后重置401处理状态
    reset401Status();

    setTimeout(() => {
      uni.switchTab({
        url: "../index/index",
      });
    }, 2000);
    return;
  }
  console.log("ref_toast.value", ref_toast.value);
  ref_toast.value?.fail(res.data.Message ?? "登录失败!");
};
const toOptions = () => {
  uni.navigateTo({
    url: "/pages/login/views/retrievePassword",
  });
};
// let codeText = ref<number | string>('获取验证码')
/** 使用微信授权登录 */
// const useWX = () => {
// 	// uni.getUserProfile({
// 	// 	desc:'请先登录',
// 	//     success: function (info) {
// 	// 		console.log('用户信息为：', info);
// 	// 	},
// 	// 	fail: function (err) {
// 	// 		console.log('用户拒绝授权',err);
// 	// 	}
// 	// });
// 	uni.login({
// 		provider: 'weixin',
// 		success: (res) => {
// 			success()
// 			console.log('code 信息 -->',res);
// 			// uni.request({
// 			// 	url: 'https://api.weixin.qq.com/sns/jscode2session',// 请求微信服务器
// 			// 	method:'GET',
// 			// 	data: {
// 			// 		appid: 'wxf2c4baa8a102ecc2',        //你的小程序的APPID
// 			// 		secret: 'dd251115c64a383ceea550cc6a03cd1b',    //你的小程序秘钥secret,
// 			// 		js_code: res.code,    //wx.login 登录成功后的code --这个是我会传给你的code
// 			// 		grant_type:'authorization_code' //此处为固定值
// 			// 	},
// 			// 	success: (res) => {
// 			// 		// success()
// 			// 		console.log('获取openid --->',res);
// 			// 	},
// 			// 	fail:(err)=> {
// 			// 		ref_toast.value?.fail('您不属于本系统用户！')
// 			// 		console.log('openid失败 --->',err);
// 			// 	}
// 			// });
// 			uni.request({
// 				url: 'http://localhost:9224/Api/V1/WxOpen/GetOpenId',
// 				header: {
// 					'Content-Type': 'application/x-www-form-urlencoded'
// 				},
// 				method: 'POST',
// 				data: {
// 					code: res.code
// 				}
// 			}).then(res => {
// 				success()
// 				console.log('获取openid --->',res);
// 			}).catch(err => {
// 				ref_toast.value?.fail('您不属于本系统用户！')
// 				console.log('openid失败 --->',err);
// 			})
// 		},
// 		fail: (err) => {
// 			success()
// 			ref_toast.value?.fail('调用失败，您当前不在微信环境')
// 			console.log(err);
// 		}
// 	})
// }
/** 使用手机号登录 */
// const getCode = () => {
// 	if (typeof codeText.value == 'string') {
// 		ref_toast.value?.success('发送成功',2500)
// 		codeText.value = '已发送'
// 		timerFN(60)
// 	} else {
// 		ref_toast.value?.warning('请勿重复获取')
// 	}
// }
// const timerFN = (time = 60) => {
// 	let timer = setInterval(() => {
// 		time -= 1
// 		codeText.value = time
// 		if (time <= 0) {
// 			clearInterval(timer)
// 			codeText.value = '获取验证码'
// 		}
// 	}, 1000)
// }
</script>

<style scoped>
.login {
  width: 100%;
  height: 100vh;
  background-color: white;
  /* background-color: skyblue; */
}

.header {
  width: 536rpx;
  height: 400rpx;
  /* 		background-image: url('../../static/mine/login_head.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		position: relative; */
}

.login_head_bg {
  filter: drop-shadow(4px 3px 3px #ccc);
}

.main {
  width: 100%;
  height: auto;
  padding: 40rpx 0rpx;
  /* border: 1px solid ; */
}

.inputBox {
  margin-top: 40rpx;
  position: relative;
  width: 80%;
  margin-left: 10%;
  border: 1px solid #ccc !important;
  border-radius: 10px !important;
}

.inputBox::before {
  content: attr(data-label);
  position: absolute;
  top: -25rpx;
  left: 40rpx;
  padding: 0rpx 20rpx;
  width: 90rpx;
  height: 40rpx;
  background-color: white !important;
  font-size: var(--size-3);
  color: var(--bk-2);
}

input {
  margin-top: 5rpx;
}

.submit {
  width: 55%;
  margin-left: 24%;
  margin-top: 30rpx;
  height: 82rpx;
  background: linear-gradient(80deg, #677bf0, #2330df);
  box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0, 0, 0, 0.2);
  border-radius: 0rpx 41rpx 41rpx 41rpx;
  color: white;
}

.fnBox1 {
  margin-top: 20rpx;
  width: 90%;
  /* border: 1px solid ; */
  text-align: right;
  font-size: 24rpx;
  line-height: 40rpx;
  color: gray;
  letter-spacing: 1rpx;
}

.verifyCode {
  transition: all 1s;
}
</style>
