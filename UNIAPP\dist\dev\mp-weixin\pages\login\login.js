"use strict";
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_index = require("../../utils/index.js");
const utils_http = require("../../utils/http.js");
const utils_request = require("../../utils/request.js");
const store_index = require("../../store/index.js");
require("../../store/pinia.js");
require("../../utils/sign.js");
require("../../utils/sha1.js");
if (!Array) {
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  (_easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_form2)();
}
const _easycom_up_input = () => "../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../node-modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_form = () => "../../node-modules/uview-plus/components/u-form/u-form.js";
if (!Math) {
  (_easycom_up_input + _easycom_up_form_item + _easycom_up_form + toast)();
}
const toast = () => "../../components/toast.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const ref_toast = common_vendor.ref();
    const ref_form = common_vendor.ref(null);
    const style = common_vendor.ref({
      marginLeft: "0px",
      fontSize: "60px"
    });
    store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    const list = common_vendor.ref([
      {
        name: "+86 (中国)",
        color: "#ffaa7f",
        fontSize: "20"
      }
    ]);
    const model = common_vendor.ref({
      form: {
        // mobile: "",
        // mobile: "18888888881",
        // mobile: "18538743360",
        // mobile: "18936261001",
        // password: "123456",
        password: ""
        // verifyCode: '',
      },
      rules: {
        "form.mobile": {
          type: "number",
          required: true,
          trigger: ["change"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请先输入手机号"));
              return;
            }
            if ((value == null ? void 0 : value.length) > 11) {
              callback(new Error("手机号过长"));
              return;
            }
            if (!utils_index.phoneRegex.test(value)) {
              callback(new Error("请输入正确的手机号"));
              return;
            }
            callback();
          }
        },
        // 'form.verifyCode': {
        // 	type: 'number',
        // 	required: true,
        // 	message: '请输入手机验证码',
        // 	trigger: ['change'],
        // },
        "form.password": {
          type: "string",
          required: true,
          trigger: ["change", "blur"],
          asyncValidator: (rule, value, callback) => {
            if (String(value).length === 0) {
              callback(new Error("请输入密码"));
              return;
            }
            if ((value == null ? void 0 : value.length) < 6) {
              callback(new Error("密码至少为6位"));
              return;
            }
            if ((value == null ? void 0 : value.length) >= 28) {
              console.error({ value });
              callback(new Error("密码输入过长"));
              return;
            }
            callback();
          }
        }
      }
    });
    const submit = () => {
      var _a;
      (_a = ref_form.value) == null ? void 0 : _a.validate().then(async (valid) => {
        if (!valid)
          return;
        getOpenid();
      }).catch((err) => {
        var _a2;
        console.log("校验失败", err);
        (_a2 = ref_toast.value) == null ? void 0 : _a2.warning("表单验证失败！");
      });
    };
    const getOpenid = () => {
      common_vendor.index.showLoading({ title: "获取中..." });
      common_vendor.index.login({
        success: (res) => {
          console.log("code 信息 -->", res);
          utils_http.getOpenidAPI({ code: res.code }).then(async (res2) => {
            var _a;
            console.log("login--openid -->", res2);
            if (res2.statusCode === 200 || res2.statusCode === 1) {
              common_vendor.index.setStorageSync("openid", res2.data.Data.openid);
              loginFn(res2.data.Data.openid);
              return;
            } else {
              (_a = ref_toast.value) == null ? void 0 : _a.info("openid缺失");
            }
          }).catch((err) => {
            var _a;
            common_vendor.index.hideLoading();
            (_a = ref_toast.value) == null ? void 0 : _a.netFail("网络请求失败");
            console.log("获取openid失败-->", err);
          });
        },
        fail: (err) => {
          var _a;
          console.log("调用失败，您当前不在微信环境 -->", err);
          (_a = ref_toast.value) == null ? void 0 : _a.netFail("请勿运行于非微信环境！");
        }
      });
    };
    const loginFn = async (openid) => {
      var _a, _b;
      if (openid) {
        model.value.form.openid = openid;
      }
      const res = await utils_http.login(model.value.form);
      console.log("登录结果 -->", res);
      if (!res)
        return;
      if (res.data.Code === 1) {
        (_a = ref_toast.value) == null ? void 0 : _a.success(res.data.Message ?? "登录成功!");
        common_vendor.index.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
        common_vendor.index.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
        userStore.userInfo = res.data.Data.User;
        utils_request.reset401Status();
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "../index/index"
          });
        }, 2e3);
        return;
      }
      console.log("ref_toast.value", ref_toast.value);
      (_b = ref_toast.value) == null ? void 0 : _b.fail(res.data.Message ?? "登录失败!");
    };
    const toOptions = () => {
      common_vendor.index.navigateTo({
        url: "/pages/login/views/retrievePassword"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(list.value[0].name),
        b: common_vendor.o(($event) => model.value.form.mobile = $event),
        c: common_vendor.p({
          placeholder: "请输入手机号",
          border: "none",
          name: "username",
          ["placeholder-style"]: "font-size:26rpx;letter-spacing: 6rpx;",
          modelValue: model.value.form.mobile
        }),
        d: common_vendor.p({
          prop: "form.mobile"
        }),
        e: common_vendor.o(($event) => model.value.form.password = $event),
        f: common_vendor.p({
          type: "password",
          border: "none",
          placeholder: "请输入密码",
          name: "password",
          ["placeholder-style"]: "font-size:26rpx;letter-spacing: 6rpx;",
          modelValue: model.value.form.password
        }),
        g: common_vendor.p({
          prop: "form.password"
        }),
        h: common_vendor.sr(ref_form, "cdfe2409-0", {
          "k": "ref_form"
        }),
        i: common_vendor.p({
          labelPosition: "left",
          model: model.value,
          rules: model.value.rules,
          labelStyle: style.value
        }),
        j: common_vendor.o(($event) => submit()),
        k: common_vendor.o(($event) => toOptions()),
        l: common_vendor.sr(ref_toast, "cdfe2409-5", {
          "k": "ref_toast"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cdfe2409"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/login/login.vue"]]);
wx.createPage(MiniProgramPage);
