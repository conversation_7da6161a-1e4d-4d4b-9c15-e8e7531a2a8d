// src/utils/request.ts
import { getCurrentEnvironment, uniToast } from "./index";
import { useUserStore } from "../store/user";
import pinia from "@/store/pinia";
import { refreshToken, getLogout } from "./http";

// 全局状态管理401错误处理
let isHandling401 = false;
let isCheckingLogin = false;

// 重置401处理状态的函数
export const reset401Status = () => {
  isHandling401 = false;
  isCheckingLogin = false;
};

export const getBaseUrl = (): string => {
  // return import.meta.env.VITE_BASE_URL;
  // return "https://192.168.1.29:9225/Api/V1"; //本机
  // return "https://192.168.1.233:9225/Api/V1";//锴哥
  return "https://360.hlktech.com/Api/V1";
};
let baseUrl = getBaseUrl();
// console.log("getBaseUrl", baseUrl);
const userStore = useUserStore(pinia);
import sign from "./sign.js";

// 拦截器配置
const httpInterceptor = {
  // 拦截前触发
  invoke(options: UniApp.RequestOptions) {
    // console.log(options);
    // 1. 非 http 开头需拼接地址  在此拼接 浏览器就代理不了了
    // if (!options.url.startsWith('http')) {
    //   options.url = baseURL + options.url
    // }
    // 2. 请求超时
    options.timeout = 5000;
    // 3. 添加小程序端请求头标识
    const headOptions = {
      "Content-Type": "application/x-www-form-urlencoded",
      //添加 token
      authorization: "Bearer " + uni.getStorageSync("AccessToken"),
      ...options.header,
    };
    options.header = sign(headOptions, "HlkTech20200429");
    console.log("全部", options);
  },
};
export interface apiConfig {
  url: string;
  method?: "GET" | "POST" | "DELETE";
  data?: any;
  header?: object;
  showLoading?: boolean;
}
// 拦截 request 请求
uni.addInterceptor("request", httpInterceptor);
// 拦截 uploadFile 文件上传
uni.addInterceptor("uploadFile", httpInterceptor);

/** 请求函数 */
export const request = async (config: apiConfig): Promise<any> => {
  // uni.hideLoading()
  if (
    config.url != "/Http2/GetEvaluation" &&
    config.url != "/Http2/GetTimeline" &&
    config.url != "/Http2/GetDepartList" &&
    config.url != "/Http2/GetUserList" &&
    config.url != "/Http2/GetUserDetail"
  ) {
    //获取题目接口不需要loading
    // uni.showLoading({ title: "加载中..." });
  }
  return await uni
    .request({
      method: config.method ?? "POST",
      header: config.header,
      //url:  `${baseUrl}`,
      url: `${baseUrl}` + config.url,
      data: config.data,
    })
    .then(async (res) => {
      console.log("这里", res);
      if (res.statusCode === 200) return await dealWidthTheCodeFail(res);
      // 后端返回状态码不规范 这里就几乎用不上  我还得在成功那里去再处理一遍。。

      switch (res.statusCode) {
        case 400:
          uniToast("服务器出错");
          break;
        case 401:
          console.log("过期");
          // 防止重复处理401错误
          if (!isHandling401) {
            isHandling401 = true;
            setTimeout(() => {
              uni.showModal({
                title: "系统提示",
                content: "您的身份信息已失效🐿️",
                confirmText: "重新授权",
                showCancel: false,
                confirmColor: "#1e90ff",
                success: () => {
                  checkLogin().finally(() => {
                    // 重置状态，允许下次401处理
                    setTimeout(() => {
                      isHandling401 = false;
                    }, 2000);
                  });
                },
              });
            }, 500);
          }
          break;
        case 403:
          uniToast("您的访问已被禁止");
          break;
        case 404:
          uniToast("访问内容已丢失");
          break;
        case 500:
          uniToast("网络请求失败");
          break;
        default:
          uniToast("遇到未知错误");
          break;
      }
      //
      uni.hideLoading();
      return false;
    })
    .catch((err) => {
      uni.hideLoading();
      console.log("请求报错 -->", err);
      return new Error("请求报错");
    });
};
/** 状态码的处理 */
const dealWidthTheCodeFail = async (res: any) => {
  const code = res.data.Code;
  return await new Promise((r, j) => {
    let errText = "";
    switch (code) {
      case 1:
        uni.hideLoading();
        r(res);
        return;
      case 2:
        uni.hideLoading();
        r(res);
        return;
      case 3:
        uni.hideLoading();
        console.log(333, res);
        isHandling401 = true; // 设置为true，阻止401弹窗
        isCheckingLogin = false;
        uni.showToast({ icon: "none", title: "请重新登录11" });
        setTimeout(() => {
          uni.redirectTo({
            url: "/pages/login/login",
          });
        }, 1000);
        // j(new Error("请重新登录"));
        return;
      case 400:
         uni.hideLoading();
        errText = "服务器出错";
        break;
      case 401:
        // uniToast('您的身份验证已过期')
        uni.hideLoading();

        // 防止重复处理401错误
        if (isHandling401) {
          j(new Error("身份验证已过期"));
          return;
        }

        isHandling401 = true;
        setTimeout(() => {
          uni.showModal({
            title: "系统提示",
            content: "您的身份信息已失效🐿️",
            confirmText: "重新授权",
            showCancel: false,
            confirmColor: "#1e90ff",
            success: () => {
              checkLogin().finally(() => {
                // 重置状态，允许下次401处理
                setTimeout(() => {
                  isHandling401 = false;
                }, 2000);
              });
            },
          });
        }, 500);
         uni.hideLoading();
        j(new Error("身份验证已过期"));
        return;
      case 403:
         uni.hideLoading();
        errText = "您的访问已被禁止";
        break;
      case 404:
         uni.hideLoading();
        errText = "访问内容已丢失";
        break;
      case 500:
         uni.hideLoading();
        errText = "网络请求失败";
        break;
      default:
         uni.hideLoading();
        errText = "遇到未知错误";
        break;
    }
    setTimeout(() => {
      uni.hideLoading();
      uniToast(errText);
      j(new Error(errText));
      // r(false)
    }, 2000);
  });
};
/** 检查是否登录 */
export const checkLogin = async () => {
  // 防止重复执行checkLogin
  if (isCheckingLogin) {
    console.log("防止重复执行checkLogin");
    return;
  }

  isCheckingLogin = true;

  try {
    // 02.看有没有token 和 openid
    if (!uni.getStorageSync("AccessToken") || !uni.getStorageSync("openid")) {
      console.log(444);
      uni.showToast({ icon: "none", title: "无效授权，请验证密码" });
      setTimeout(() => {
        uni.redirectTo({
          url: "/pages/login/login",
        });
      }, 200);
      return;
    }
    // 03.有token 及 openid 验证一下是否有过期
    await getCurrentEnvironment()
      .then(async (url) => {
        if (url.length >= 5) {
          //微信环境才去拿openid-刷新用户信息
          const res = await refreshToken({
            OpenId: uni.getStorageSync("openid"),
          });
          if (!res.data.Data) {
            uni.showToast({ icon: "none", title: res.data.Message });
            return;
          }
          uni.setStorageSync("AccessToken", res.data.Data.Token.AccessToken);
          uni.setStorageSync("RefreshToken", res.data.Data.Token.RefreshToken);
          // uni.setStorageSync('userInfo', JSON.stringify(res.data.Data.User))
          userStore.userInfo = res.data.Data.User;
          console.log("刷新token -->", res);
        }
      })
      .catch((err) => {
        // new Error(err);
        console.error("环境检测失败:", err);
      });
  } catch (error) {
    console.error("checkLogin error:", error);
  } finally {
    // 重置状态，允许下次执行
    isCheckingLogin = false;
  }
};
