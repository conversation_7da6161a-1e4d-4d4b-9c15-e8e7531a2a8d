<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="back()">
        <image class="return-img" src="../../../static/icons/fanhui.png" mode="aspectFit"></image>
      </view>
      <text class="title">历史评测</text>
    </view>
    <view class="extendHead"></view>
    <view class="yearBox">
      当前年份:
      <view class="year">
        <picker mode="selector" :range="yearRrange" @change="handleConfirm">
          {{ currentYear }}
        </picker>
      </view>
      <up-icon name="map"></up-icon>

    </view>
    <view class="box">
      <view class="" style="margin-top: 20vh" v-if="showLoading">
        <up-loading-icon size="3vh"></up-loading-icon>
        <view style="
            text-align: center;
            font-size: 24rpx;
            color: #ccc;
            margin-top: 15rpx;
            letter-spacing: 1rpx;
          ">
          数据载入中...
        </view>
      </view>
      <up-collapse :border="false" v-else>
        <up-collapse-item :disabled="item.tap === ' 暂无数据'" :name="item.Name" style="font-size: 36rpx"
          v-for="(item, index) in list" :key="item">
          <template #title>
            <view style="display: flex">
              <view class="">
                {{ item.Name }}
              </view>
              <view :class="'tag'" v-if="item.tap != false">
                {{ item.tap }}
              </view>
              <view v-else class="look">
                <!--<up-icon name="fingerprint" color="#2979ff" size="35"></up-icon> -->
                查看
              </view>
            </view>
          </template>
          <view class="cu-chat" v-for="(item1, index2) in funcList" :key="item1.id" data-selected="false"
            @click="handleSelect({ ...item1, ...item })">
            <view class="cu-item" v-if="item1.admin">
              {{ item1.name }}
            </view>
          </view>
        </up-collapse-item>
      </up-collapse>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { useIndexStore } from "@/store";
import { useUserStore } from "@/store/user";
import { getTheYearMonths, getYearsDistanceFrom2024 } from "@/utils";
import { getHistoryList } from "@/utils/http";
const store = useIndexStore();
const userStore = useUserStore();
console.log('userStore', userStore);

const funcList = [
  {
    name: "自评",
    id: 0,
    admin: true
  },
  {
    name: "互评",
    id: 1,
    admin: !userStore.IsDepartmentlanager
  },
  {
    name: "上级对下级",
    id: 2,
    admin: userStore.IsDepartmentlanager
  },
  {
    name: "下级对上级",
    id: 3,
    admin: true
  },
  {
    name: "月评",
    id: 4,
    admin: true,
    QueryType: "month"
  },
  {
    name: "季评",
    id: 5,
    admin: true,
    QueryType: "quarter"
  },
  {
    name: "年评",
    id: 6,
    admin: true,
    QueryType: "year"
  },
];
const list = ref([
  {
    Name: "2024年10月",
    tap: "",
  },
]);

const showLoading = ref(true);
const currentYear = ref<number>(new Date().getFullYear());
const yearRrange = ref(getYearsDistanceFrom2024()); //可选年份数组 2024年起
/** 重新计算当前年1月到今月 */
const reComputedList = (year?: number) => {
  const yearList = getTheYearMonths(year);
  list.value = [];
  // yearList.reverse()
  // console.log('年份呢',yearList);
  for (let i = 0; i < yearList.length; i++) {
    list.value[i] = {
      Name: yearList[i],
      tap: "",
    };
  }
};
reComputedList();
/** 选择 */
const handleSelect = (item: any) => {
  console.log(item);
  if (item.id < 4) {
    uni.navigateTo({
      url: "./tablePage?type=" + item.id + "&date=" + item.Name,
    });
    return;
  } else {
    const [year, month] = item.Name.split('-');
    if (item.id == 6) {
      item.Name = `${year}`;
    }
    // 季评去0
    if (item.id == 5) {
      const monthNum = Number(month);
      let quarterMonth;

      if (monthNum >= 1 && monthNum <= 3) {
        quarterMonth = 1;
      } else if (monthNum >= 4 && monthNum <= 6) {
        quarterMonth = 2;
      } else if (monthNum >= 7 && monthNum <= 9) {
        quarterMonth = 3;
      } else {
        quarterMonth = 4;
      }
      item.Name = `${year}-${quarterMonth}`;
    }
    uni.navigateTo({
      url: `/pageA/pages/statistics/statistics?TimeDate=${item.Name}&QueryType=${item.QueryType}&title=${item.name}`,
    });

  }
  // 中间层
  // uni.navigateTo({
  //   url: "./middlePage?type=" + item.id + "&date=" + item.Name,
  // });
};
// 定义 open 方法
const handleConfirm = async (e) => {
  showLoading.value = true;
  const index = e.detail.value;
  currentYear.value = yearRrange.value[index];
  reComputedList(yearRrange.value[index]);
  await getHistoryAPI();
};


const getHistoryAPI = async () => {
  const res = await getHistoryList({ TimeDate: currentYear.value });
  const data = res.data?.Data?.listjson;
   // 添加数据验证
  if (!data || !Array.isArray(data)) {
    console.log('数据为空');
    showLoading.value = false;
    return;
  }
  list.value.map((item, index) => {
    if (data[index]) {
      item.Name = data[index].TimeDate;
      item.tap = ` ${data[index].Value}`;
    }
  });
  list.value.reverse();
  console.log(list.value);
  showLoading.value = false;
};
getHistoryAPI();
// getHistoryList

const back = () => {
  uni.switchTab({
    url: '/pages/work/work'
  })
}

</script>

<style scoped>
.head {
  background: linear-gradient(81deg, #586ce2, #2330df);
  color: white;

}

.extendHead {
  background: linear-gradient(81deg, #586ce2, #2330df);
  height: 140rpx;
  margin-top: -2px;
  padding: 0;
  position: relative;
}



.box {
  position: absolute;
  top: 240rpx;
  width: 90%;
  min-height: 85vh;
  margin-left: 5%;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(82, 102, 226, 0.1);
  overflow: auto !important;
}

.yearBox {
  position: absolute;
  top: 200rpx;
  left: 5vw;
  min-width: 260rpx;
  z-index: 100;
  padding: 15rpx 10rpx 14rpx 15rpx;
  background-color: white;
  border-radius: 10px 10px 0px 0px;
  color: #666666;
  display: flex;
  /* border: 1px solid #000; */
}

.year {
  border-bottom: 1px solid #02d7ee;
  letter-spacing: 1px;
  color: dodgerblue;

}

.cu-item {
  width: 100% !important;
  padding: 40rpx;
  font-size: 30rpx;
  border-top: 1px solid #eef1fe;
}

.tag {
  font-size: 22rpx;
  margin-left: auto;
  /* margin-left: 20rpx; */
  margin-right: 120rpx;
  padding: 0rpx 0rpx 5rpx 5rpx;
  width: 2px;
  height: 30rpx;
  box-shadow: 0 0 4px #ccc;
  border-radius: 5px;
  background-color: dodgerblue;
  color: #666666;
  white-space: nowrap;
  text-indent: 10rpx;
}

.look {
  margin-left: auto;
  margin-right: 15rpx;
  color: #2979ff;
  font-size: 14px;

}
</style>
