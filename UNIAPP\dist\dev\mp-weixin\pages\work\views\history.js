"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const store_user = require("../../../store/user.js");
const utils_index = require("../../../utils/index.js");
const utils_http = require("../../../utils/http.js");
require("../../../utils/request.js");
require("../../../store/pinia.js");
require("../../../utils/sign.js");
require("../../../utils/sha1.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_collapse_item2 = common_vendor.resolveComponent("up-collapse-item");
  const _easycom_up_collapse2 = common_vendor.resolveComponent("up-collapse");
  (_easycom_up_icon2 + _easycom_up_loading_icon2 + _easycom_up_collapse_item2 + _easycom_up_collapse2)();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_loading_icon = () => "../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_collapse_item = () => "../../../node-modules/uview-plus/components/u-collapse-item/u-collapse-item.js";
const _easycom_up_collapse = () => "../../../node-modules/uview-plus/components/u-collapse/u-collapse.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_loading_icon + _easycom_up_collapse_item + _easycom_up_collapse)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "history",
  setup(__props) {
    store_index.useIndexStore();
    const userStore = store_user.useUserStore();
    console.log("userStore", userStore);
    const funcList = [
      {
        name: "自评",
        id: 0,
        admin: true
      },
      {
        name: "互评",
        id: 1,
        admin: !userStore.IsDepartmentlanager
      },
      {
        name: "上级对下级",
        id: 2,
        admin: userStore.IsDepartmentlanager
      },
      {
        name: "下级对上级",
        id: 3,
        admin: true
      },
      {
        name: "月评",
        id: 4,
        admin: true,
        QueryType: "month"
      },
      {
        name: "季评",
        id: 5,
        admin: true,
        QueryType: "quarter"
      },
      {
        name: "年评",
        id: 6,
        admin: true,
        QueryType: "year"
      }
    ];
    const list = common_vendor.ref([
      {
        Name: "2024年10月",
        tap: ""
      }
    ]);
    const showLoading = common_vendor.ref(true);
    const currentYear = common_vendor.ref((/* @__PURE__ */ new Date()).getFullYear());
    const yearRrange = common_vendor.ref(utils_index.getYearsDistanceFrom2024());
    const reComputedList = (year) => {
      const yearList = utils_index.getTheYearMonths(year);
      list.value = [];
      for (let i = 0; i < yearList.length; i++) {
        list.value[i] = {
          Name: yearList[i],
          tap: ""
        };
      }
    };
    reComputedList();
    const handleSelect = (item) => {
      console.log(item);
      if (item.id < 4) {
        common_vendor.index.navigateTo({
          url: "./tablePage?type=" + item.id + "&date=" + item.Name
        });
        return;
      } else {
        const [year, month] = item.Name.split("-");
        if (item.id == 6) {
          item.Name = `${year}`;
        }
        if (item.id == 5) {
          const monthNum = Number(month);
          let quarterMonth;
          if (monthNum >= 1 && monthNum <= 3) {
            quarterMonth = 1;
          } else if (monthNum >= 4 && monthNum <= 6) {
            quarterMonth = 2;
          } else if (monthNum >= 7 && monthNum <= 9) {
            quarterMonth = 3;
          } else {
            quarterMonth = 4;
          }
          item.Name = `${year}-${quarterMonth}`;
        }
        common_vendor.index.navigateTo({
          url: `/pageA/pages/statistics/statistics?TimeDate=${item.Name}&QueryType=${item.QueryType}&title=${item.name}`
        });
      }
    };
    const handleConfirm = async (e) => {
      showLoading.value = true;
      const index = e.detail.value;
      currentYear.value = yearRrange.value[index];
      reComputedList(yearRrange.value[index]);
      await getHistoryAPI();
    };
    const getHistoryAPI = async () => {
      var _a, _b;
      const res = await utils_http.getHistoryList({ TimeDate: currentYear.value });
      const data = (_b = (_a = res.data) == null ? void 0 : _a.Data) == null ? void 0 : _b.listjson;
      if (!data || !Array.isArray(data)) {
        console.log("数据为空");
        showLoading.value = false;
        return;
      }
      list.value.map((item, index) => {
        if (data[index]) {
          item.Name = data[index].TimeDate;
          item.tap = ` ${data[index].Value}`;
        }
      });
      list.value.reverse();
      console.log(list.value);
      showLoading.value = false;
    };
    getHistoryAPI();
    const back = () => {
      common_vendor.index.switchTab({
        url: "/pages/work/work"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => back()),
        b: common_vendor.t(currentYear.value),
        c: yearRrange.value,
        d: common_vendor.o(handleConfirm),
        e: common_vendor.p({
          name: "map"
        }),
        f: showLoading.value
      }, showLoading.value ? {
        g: common_vendor.p({
          size: "3vh"
        })
      } : {
        h: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.Name),
            b: item.tap != false
          }, item.tap != false ? {
            c: common_vendor.t(item.tap),
            d: common_vendor.n("tag")
          } : {}, {
            e: common_vendor.f(funcList, (item1, index2, i1) => {
              return common_vendor.e({
                a: item1.admin
              }, item1.admin ? {
                b: common_vendor.t(item1.name)
              } : {}, {
                c: item1.id,
                d: common_vendor.o(($event) => handleSelect({
                  ...item1,
                  ...item
                }), item1.id)
              });
            }),
            f: item,
            g: "284ecb32-3-" + i0 + ",284ecb32-2",
            h: common_vendor.p({
              disabled: item.tap === " 暂无数据",
              name: item.Name
            })
          });
        }),
        i: common_vendor.p({
          border: false
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-284ecb32"], ["__file", "E:/WebiApp/360jiXiao/UNIAPP/src/pages/work/views/history.vue"]]);
wx.createPage(MiniProgramPage);
