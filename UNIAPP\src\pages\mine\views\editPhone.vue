<template>
	<view class="pageBox">
		<view class="head">
			<view class="return" @click="store.backAPage()">
				<image class="return-img" src="../../../static/icons/fanhui.png" mode="aspectFit"></image>
			</view>
			<view class="title">换绑手机号</view>
		</view>
		<view class="main">
			<view class="item" v-for="item,index in list" :key="index" style="border-bottom: 1px solid #E5E5E5;">
				<up-input class="input" type="text" v-model="item.value" border="none" :placeholder="item.text">
					<template #prefix>
						<view style="border-right:1rpx solid #E5E5E5;padding-right: 30rpx;display: flex;place-items: center;">
							<view class="">
								{{item.name}}
							</view>
							<view class=""  v-if="index != 0">
								<!-- <up-icon name="email" size="30"></up-icon> -->
							</view>
						</view>
					</template>
					<template #suffix v-if="index === 1">
						<view class="" style="margin-right: 30rpx;">
							<up-button type="primary" plain size="small" @click="getCode()">
								<view class="">
									{{codeText}}
								</view>
								<view class="" v-if="codeText === '获取验证码' || codeText === '重新发送'">
									<up-icon name="email" size="32" color="#2979ff"></up-icon>
								</view>
							</up-button>
						</view>
					</template>
				</up-input>
			</view>
			<view class="text" style="transition: all .5s;" @click="none()">
				无法接收验证码？
			</view>
			<view class="" style="margin-top: 10rpx;transition: all .5s;">
				<button  style="width: 80%;background: linear-gradient(80deg, #677BF0, #2330DF);
					box-shadow: 0rpx 3rpx 6rpx 0rpx rgba(0,0,0,0.2);color: white;
					border-radius: 0rpx 41rpx 41rpx" @click="Submit()">提交</button>
			</view>
			<!--  -->
		</view>
		<!-- <validatePhonePage v-model:config="validatePhonePageConfig" @onSubmit="handleSubmit" v-else></validatePhonePage> -->
		<toast ref="ref_toast"></toast>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { useIndexStore } from '@/store';
	// import validatePhonePage from '@/pages/componentPage/validatePhonePage.vue'
	import  toast  from '@/components/toast.vue'
	import type { IToast } from '@/types';
	import { onLoad } from '@dcloudio/uni-app';
	import { phoneRegex } from '@/utils';
	import { sendSms, updatePhone } from '@/utils/http';
	import { useUserStore } from '@/store/user';
	const userStore = useUserStore()
	const ref_toast = ref<IToast>()
	const list = ref([
		{ name: '手机号',value:'',text:'请输入新手机号' },
		{ name: '验证码',value:'',text:'请输入验证码' }, 
	])
	// const showPage = ref(true) //true本页面，false 确认新手机号接受换绑页面
	// const validatePhonePageConfig = ref([
	// 	{ name: '密码',value:'',text:'请输入要绑定的新手机号',key:'Mobile' },
	// 	{ name: '验证码',value:'',text:'请输入验证码',key:'Code' }, 
	// ])
	const codeText = ref('获取验证码')
	const store = useIndexStore()
	// const toRouter = () => {
	// 	uni.redirectTo({
	// 		url: "/pages/login/login", 
	// 	})
	// }
	// 函数
	const getCode = async ()=>{
		if(codeText.value == '获取验证码' || codeText.value == '重新发送' ){
			await sendSms({Mobile:list.value[0].value,SmsType:'findphone'})
			.then(res=>{
				// console.log('短信',res);
				if(res.data.Code!=1){
					ref_toast.value?.info(res.data.Message)
					return
				}
				ref_toast.value?.success('发送成功')
				codeText.value = '已发送'
				timerFN(60)	
			})
		}else{
			ref_toast.value?.warning('请勿重复获取')
		}
	}
	const timerFN = (time = 60) =>{
		let timer = setInterval(()=>{
			time -= 1
			codeText.value = '已发送：' + time
			if(time <= 0){
				clearInterval(timer)
				codeText.value = '重新发送'
			}
		},1000)
	}
	const Submit = ()=>{
		if(!phoneRegex.test(list.value[0].value)){
			ref_toast.value?.warning('请填写正确的手机号')
			return
		}
		if(codeText.value === '获取验证码'){
			ref_toast.value?.warning('请点击获取验证码')
			return
		}
		if(!list.value[1].value){
			ref_toast.value?.warning('请填写验证码')
			return
		}
		uni.showLoading({
			title:'短信校验中...'
		})
		setTimeout(()=>{
			uni.hideLoading()
			handleSubmit({Mobile:list.value[0].value,Code:Number(list.value[1].value)})
			// showPage.value = false
		},2000)
	}
	/** 子组件的提交方法 会返回传递的对象 */
	const handleSubmit = async (e: { Mobile: string; Code: number })=>{
		await updatePhone(e)
		.then(res=>{
			console.log('换绑手机情况 -->',res);
			if(res.data.Code!=1){
				ref_toast.value?.fail(res.data.Message)
				return
			}
			userStore.userInfo.Mobile = list.value[0].value
			ref_toast.value?.success('换绑成功')
			setTimeout(()=>{
				uni.navigateBack({
					delta:1
				})
			},1500)
		})
		// console.log(e)
	}
	const none = ()=>{
		ref_toast.value?.info('请联系管理员')
	}
	onLoad((e)=>{
		list.value[0].value = e.data
	})
</script>

<style scoped>
	.head{
		height: 170rpx;
		background-color: white;
		display: flex;
		place-items: center;
	}
	.head>view{
		margin-top: 40rpx;
	}
	.main {
		margin-top: 40rpx;
		width: 100%;
		height: 800rpx;
		/* border: 1px solid ; */
	}
	
	.main>.item {
		width: 90%;
		height: 100rpx;
		font-size: 32rpx;
		padding: 10rpx 0rpx;
		margin: 25rpx auto;
		/* border: 1px solid gray; */
		background-color: white;
		display: flex;
		color: #666666;
		place-items: center;
	}
	
	.item>view {
		width: 50%;
		text-align: right;
	}
	
	.item>view>image {
		margin-top: 10rpx;
		width: 38rpx;
		height: 30rpx;
		/* border: 1px solid gray; */
	}
	.text{
		text-align: right;
		font-size: 20rpx;
		margin-right: 60rpx;
		padding-bottom: 10rpx;
		color: #888888;
		letter-spacing: 2rpx;
	}
	.input{
		letter-spacing: 3rpx;
	}
</style>