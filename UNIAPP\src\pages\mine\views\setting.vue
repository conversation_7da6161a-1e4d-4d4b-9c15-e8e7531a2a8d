<template>
  <view class="pageBox">
    <view class="head">
      <view class="return" @click="store.backAPage()">
        <image
          class="return-img"
          src="../../../static/icons/fanhui.png"
          mode="aspectFit"></image>
      </view>
      <view class="title">设置</view>
    </view>
    <view class="main">
      <view
        class="item"
        v-for="(item, index) in list"
        :key="index"
        @click="toRouter(index)">
        <view style="width: 10%"></view>
        <view style="text-align: left">{{ item.name }}</view>
        <view
          style="font-size: 26rpx; margin-right: 10rpx; color: #666666"
          v-if="index === 1">
          {{ item.icon }}
        </view>
        <view class="icon" v-else>
          <view style="margin-left: auto">
            <up-icon :name="item.icon" size="50"></up-icon>
          </view>
        </view>
        <view style="flex: 1; text-align: right; margin-right: 20px">
          <image
            style="margin-left: 0rpx"
            src="../../../static/work/back.png"
            mode="aspectFit"></image>
        </view>
      </view>
    </view>
    <!-- <up-action-sheet :actions="list2" :title="'系统提示'" :show="showSheet"></up-action-sheet> -->
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useIndexStore } from "@/store";
import { useUserStore } from "@/store/user";
import { onShow } from "@dcloudio/uni-app";
import { getLogout } from "@/utils/http";
const userStore = useUserStore();
const list = ref([
  { name: "修改密码", icon: "email" },
  { name: "更换手机号", icon: `${userStore.userInfo.Mobile}` },
  { name: "退出登录", icon: "" },
]);
const store = useIndexStore();
onShow(() => {
  list.value[1].icon = userStore.userInfo.Mobile ?? "";
});
// const showSheet = ref(true)
// const list2 = ref([
//   {
//     name: '选项一',
//     subname: "选项一描述",
//     color: '#ffaa7f',
//     fontSize: '20'
//   },
//   {
//     name: '选项二禁用',
//     disabled: true
//   },
//   {
//     name: '开启load加载', // 开启后文字不显示
//     loading: true
//   }
// ]);
const getLogoutAPI = async () => {
  const obj = {
    AccessToken: uni.getStorageSync("AccessToken"),
    RefreshToken: uni.getStorageSync("RefreshToken"),
  };
  const res = await getLogout(obj);
  console.log("res", res);
  uni.hideLoading();
  const code = res.data.Code;
  if (code === 1) {
    uni.showToast({
      title: "退出成功",
      icon: "none",
    });
  } else {
    uni.showToast({
      title: "退出失败",
      icon: "none",
    });
  }
};
const toRouter = (index: number) => {
  switch (index) {
    case 0:
      uni.navigateTo({
        url: "./editPassword",
      });
      return;
    case 1:
      uni.navigateTo({
        url: "./editPhone",
      });
      return;
    case 2:
      uni.showActionSheet({
        itemList: [`确认登出，为您安全退出账号`, `并清除敏感信息`],
        itemColor: "#772E40",
        success: function (res) {
          getLogoutAPI();
          setTimeout(() => {
            userStore.resetLoginData();
            uni.navigateTo({
              url: "/pages/login/login", 
            });
          }, 1000);
        },
      });

      return;
    default:
      return;
  }
};
</script>

<style scoped>
/deep/ .uni-actionsheet__cell {
  font-size: 28rpx !important;
}

.head {
  height: 170rpx;
  background-color: white;
  display: flex;
  place-items: center;
}

.head > view {
  margin-top: 40rpx;
}

.pageBox {
  background-color: #f5f5f5;
}

.main {
  width: 100%;
  height: 800rpx;
  /* border: 1px solid ; */
}

.main > .item {
  width: 100%;
  height: 120rpx;
  padding: 10rpx 0rpx;
  margin: 25rpx auto;
  /* border: 1px solid gray; */
  background-color: white;
  display: flex;
  color: #666666;
  place-items: center;
}

.item > view {
  width: 50%;
  text-align: right;
  font-size: 32rpx;
  /* width: 100%;
		height: 100%;
		line-height: 60rpx;
		font-size: var(--size-4);
		letter-spacing: 2rpx;
		display: flex;
		justify-content: center;
		place-items: center; */
  /* border: 1px solid #ccc; */
}

.item > view > image {
  margin-top: 10rpx;
  width: 38rpx;
  height: 30rpx;
  /* border: 1px solid gray; */
}

.icon {
  margin-right: 10rpx;
  margin-top: -7rpx;
  display: flex;
  place-content: center;
}
</style>
